/// Link transformation system for converting between different markdown link formats.
///
/// # Overview
/// This module provides a flexible system for transforming markdown links between different
/// formats (inline, reference, etc.) while maintaining proper reference numbering and avoiding
/// conflicts. The system is designed around a delegation pattern that separates the public
/// API from the stateful transformation logic.
///
/// # Architecture
/// The transformation system consists of several cooperating components:
///
/// ## Core Components
/// - [`LinkTransform`]: Public enum defining transformation strategies
/// - [`LinkTransformer`]: Main orchestrator that manages transformation state
/// - [`LinkTransformState`]: Internal enum holding strategy-specific state
/// - [`LinkTransformation`]: Temporary holder for preprocessing data
/// - [`ReferenceAssigner`]: Stateful numbering system for reference links
///
/// ## Data Flow
/// ```text
/// LinkTransform (user choice)
///       ↓
/// LinkTransformer::from() → LinkTransformState (with ReferenceAssigner if needed)
///       ↓
/// For each link:
///   LinkTransformation::new() → extract preprocessing data
///       ↓
///   LinkTransformation::apply() → perform transformation using state
/// ```
///
/// ## Ownership Workaround
/// The two-step process (new → apply) exists because Rust's borrowing rules prevent:
/// ```text
/// let result = transformer.transform(&mut self, link)
/// //           ^^^^^^^^^^^            ^^^^
/// //           first borrow           second borrow
/// ```
/// By extracting data in `new()` and applying in `apply()`, we can release the borrow
/// between steps.
///
/// # Transformation Strategies
/// - **Keep**: No changes, preserve original format
/// - **Inline**: Convert all to `[text](url)` format
/// - **Reference**: Convert all to `[text][1]` with renumbering
/// - **NeverInline**: Convert only inline links to references, preserve existing references

use crate::md_elem::elem::*;
use crate::md_elem::*;
use crate::output::fmt_md_inlines::{InlineElemOptions, LinkLike, MdInlinesWriter};
use crate::util::output::Output;
use clap::ValueEnum;
use std::borrow::Cow;
use std::collections::hash_map::Entry;
use std::collections::HashMap;
use std::ops::Deref;

/// Whether to render links as inline, reference form, or keep them as they were.
#[derive(Copy, Clone, Debug, PartialEq, Eq, PartialOrd, Ord, Hash, Default, ValueEnum)]
#[non_exhaustive]
pub enum LinkTransform {
    /// Keep links as they were in the original
    Keep,

    /// Turn all links into inlined form: `[link text](https://example.com)`
    Inline,

    /// Turn all links into reference form: `[link text][1]`
    ///
    /// Links that weren't already in reference form will be auto-assigned a reference id. Links that were in reference
    /// form will have the link number be reordered.
    Reference,

    #[default]
    /// Keep [`Full`], [`Collapsed`], and [`Shortcut`] as they are, but replace links.
    ///
    /// The current implementation will turn them into full-style links with incrementing numbers, but this may
    /// change in future versions.
    ///
    /// [`Full`]: LinkReference::Full
    /// [`Collapsed`]: LinkReference::Collapsed
    /// [`Shortcut`]: LinkReference::Shortcut
    /// [`Inline`]: LinkReference::Inline
    NeverInline,
}

/// The main orchestrator for link transformations.
///
/// This struct coordinates the link transformation process by managing the stateful
/// components needed for each [`LinkTransform`] variant. It acts as a facade that
/// hides the complexity of the different transformation strategies.
///
/// # Architecture
/// The transformer uses a delegation pattern where the actual transformation logic
/// is handled by [`LinkTransformState`] variants. This allows each transformation
/// type to maintain its own state (like [`ReferenceAssigner`] for numbering) while
/// providing a uniform interface.
///
/// # Usage Flow
/// 1. **Creation**: `LinkTransformer::from(LinkTransform::Reference)` creates the transformer
/// 2. **Preprocessing**: For each link, call [`LinkTransformation::new()`] to extract needed data
/// 3. **Transformation**: Call [`LinkTransformation::apply()`] to perform the actual transformation
/// 4. **Introspection**: Use [`transform_variant()`] to get the current transform type
///
/// # Ownership Workaround
/// The two-step process (new → apply) exists to work around Rust's borrowing rules.
/// See [`LinkTransformation`] for details.
///
/// [`transform_variant()`]: LinkTransformer::transform_variant
pub(crate) struct LinkTransformer {
    delegate: LinkTransformState,
}

/// Represents the display text of a link, which can be either plain text or formatted inlines.
///
/// This enum handles the two ways link text can be represented in the markdown AST:
/// - Simple text strings (most common case)
/// - Complex inline formatting like `[**bold** text](url)`
///
/// Used primarily by [`ReferenceAssigner`] when it needs to convert link labels to
/// strings for reference IDs (e.g., `[text][]` → `[text][text]`).
#[derive(Debug, PartialEq, Eq, Hash, Clone)]
pub(crate) enum LinkLabel<'md> {
    /// Plain text label, potentially borrowed from the original markdown
    Text(Cow<'md, str>),
    /// Complex label with inline formatting (emphasis, strong, etc.)
    Inline(&'md Vec<Inline>),
}

impl<'md> LinkLabel<'md> {
    /// Converts the label to a plain string for use in reference IDs.
    ///
    /// For [`LinkLabel::Text`], this is straightforward string conversion.
    /// For [`LinkLabel::Inline`], this renders the formatted inlines back to
    /// markdown text (preserving formatting like `**bold**`).
    ///
    /// Used when converting collapsed/shortcut references to full references,
    /// where the label becomes the reference ID: `[text][]` → `[text][text]`.
    pub(crate) fn get_sort_string(&self, ctx: &'md MdContext) -> String {
        // There may be a way to Cow this so that we don't have to copy the ::Text string, but I can't find it.
        match self {
            LinkLabel::Text(s) => s.to_string(),
            LinkLabel::Inline(inlines) => {
                let mut inline_writer = MdInlinesWriter::new(
                    ctx,
                    InlineElemOptions {
                        link_format: LinkTransform::Keep,
                        renumber_footnotes: false,
                    },
                );
                inlines_to_string(&mut inline_writer, inlines)
            }
        }
    }
}

impl From<LinkTransform> for LinkTransformer {
    /// Creates a new transformer with the appropriate internal state for the given strategy.
    ///
    /// This is where the delegation pattern is established:
    /// - [`LinkTransform::Keep`] and [`LinkTransform::Inline`] need no state
    /// - [`LinkTransform::Reference`] and [`LinkTransform::NeverInline`] get a [`ReferenceAssigner`]
    ///   for managing reference numbering
    ///
    /// The [`ReferenceAssigner`] starts with `next_index: 1` and an empty reordering map.
    fn from(value: LinkTransform) -> Self {
        let delegate = match value {
            LinkTransform::Keep => LinkTransformState::Keep,
            LinkTransform::Inline => LinkTransformState::Inline,
            LinkTransform::Reference => LinkTransformState::Reference(ReferenceAssigner::new()),
            LinkTransform::NeverInline => LinkTransformState::NeverInline(ReferenceAssigner::new()),
        };
        Self { delegate }
    }
}

/// Internal state holder for different transformation strategies.
///
/// This enum implements the delegation pattern by holding strategy-specific state.
/// The stateless variants ([`Keep`], [`Inline`]) require no additional data,
/// while the stateful variants ([`Reference`], [`NeverInline`]) contain a
/// [`ReferenceAssigner`] to manage reference numbering.
///
/// This separation allows the public [`LinkTransform`] enum to remain simple
/// while the internal implementation can maintain complex state as needed.
///
/// [`Keep`]: LinkTransformState::Keep
/// [`Inline`]: LinkTransformState::Inline
/// [`Reference`]: LinkTransformState::Reference
/// [`NeverInline`]: LinkTransformState::NeverInline
enum LinkTransformState {
    /// Keep all links unchanged - no state needed
    Keep,
    /// Convert all links to inline format - no state needed
    Inline,
    /// Convert all links to numbered references - needs numbering state
    Reference(ReferenceAssigner),
    /// Convert only inline links to references - needs numbering state
    NeverInline(ReferenceAssigner),
}

/// Temporary holder for preprocessing data needed during link transformation.
///
/// This struct exists to work around Rust's borrowing rules by separating the
/// data extraction phase from the transformation phase. It holds any preprocessing
/// data that needs to be computed before the actual transformation can occur.
///
/// # Borrowing Workaround
/// Without this pattern, we'd need to do:
/// ```text
/// let result = transformer.transform(&mut self, link)
/// //           ^^^^^^^^^^^            ^^^^
/// //           first borrow           second borrow (ERROR!)
/// ```
///
/// Instead, we can do:
/// ```text
/// let transformation = LinkTransformation::new(variant, writer, link); // extract data
/// let result = transformation.apply(&mut transformer, link_ref);       // use data
/// ```
///
/// # Current Data
/// Currently only holds `link_text` for [`LinkTransform::Reference`], which needs
/// to extract the text from collapsed/shortcut links to use as reference IDs.
/// Other transform types don't need preprocessing data.
pub(crate) struct LinkTransformation<'md> {
    /// Text extracted from collapsed/shortcut links for use as reference IDs.
    /// Only populated for [`LinkTransform::Reference`] when processing
    /// [`LinkReference::Collapsed`] or [`LinkReference::Shortcut`].
    link_text: Option<Cow<'md, str>>,
}

impl<'md> LinkTransformation<'md> {
    /// Extracts preprocessing data needed for the given transformation strategy.
    ///
    /// This is the first phase of the two-phase transformation process. It examines
    /// the link and transformation type to determine what data needs to be extracted
    /// before the actual transformation can occur.
    ///
    /// # Current Preprocessing
    /// - [`LinkTransform::Reference`]: Extracts text from collapsed/shortcut links
    ///   to use as reference IDs (e.g., `[text][]` → `[text][text]`)
    /// - Other transforms: No preprocessing needed, returns empty state
    ///
    /// # Parameters
    /// - `transform`: The transformation strategy to apply
    /// - `inline_writer`: Writer for converting complex inlines to strings
    /// - `item`: The link-like item being processed (must implement [`LinkLike`])
    pub(crate) fn new<L>(transform: LinkTransform, inline_writer: &mut MdInlinesWriter<'md>, item: L) -> Self
    where
        L: LinkLike<'md> + Copy,
    {
        let link_text = match transform {
            LinkTransform::Keep | LinkTransform::Inline => None,
            LinkTransform::Reference => {
                let (_, label, definition) = item.link_info();
                match &definition.reference {
                    LinkReference::Inline | LinkReference::Full(_) => None,
                    LinkReference::Collapsed | LinkReference::Shortcut => {
                        let text = match label {
                            LinkLabel::Text(text) => text,
                            LinkLabel::Inline(text) => Cow::Owned(inlines_to_string(inline_writer, text)),
                        };
                        Some(text)
                    }
                }
            }
            LinkTransform::NeverInline => None,
        };
        Self { link_text }
    }

    /// Applies the transformation using the preprocessed data and transformer state.
    ///
    /// This is the second phase of the two-phase transformation process. It uses
    /// the data extracted in [`new()`] along with the transformer's internal state
    /// to perform the actual link transformation.
    ///
    /// # Transformation Logic
    /// - [`LinkTransformState::Keep`]: Returns the original link unchanged
    /// - [`LinkTransformState::Inline`]: Converts all links to inline format
    /// - [`LinkTransformState::Reference`]: Uses [`ReferenceAssigner`] to convert to numbered references
    /// - [`LinkTransformState::NeverInline`]: Converts only inline links, preserves others
    ///
    /// # Parameters
    /// - `transformer`: Mutable reference to the transformer (for accessing/updating state)
    /// - `link`: The original link reference to transform
    ///
    /// # Returns
    /// The transformed [`LinkReference`]. Always returns an owned value to simplify
    /// memory management (could be optimized to return `Cow` in the future).
    ///
    /// [`new()`]: LinkTransformation::new
    // We could in principle return a Cow<'md, LinkReference>, and save some clones in the assigner.
    // To do that, fmt_md_inlines.rs would need to adjust to hold Cows instead of LinkLabels directly. For now, not
    // a high priority.
    pub(crate) fn apply(self, transformer: &mut LinkTransformer, link: &'md LinkReference) -> LinkReference {
        match &mut transformer.delegate {
            LinkTransformState::Keep => Cow::Borrowed(link),
            LinkTransformState::Inline => Cow::Owned(LinkReference::Inline),
            LinkTransformState::Reference(assigner) => assigner.assign(self, link),
            LinkTransformState::NeverInline(assigner) => {
                match link {
                    LinkReference::Inline => assigner.assign_new(),
                    LinkReference::Full(prev) => {
                        // For NeverInline, reserve the number but keep the original reference
                        assigner.reserve_if_numeric(prev);
                        Cow::Borrowed(link)
                    }
                    LinkReference::Collapsed | LinkReference::Shortcut => Cow::Borrowed(link),
                }
            }
        }
        .into_owned()
    }
}

impl LinkTransformer {
    /// Returns the transformation variant this transformer was created with.
    ///
    /// This is used for introspection, particularly in the two-phase transformation
    /// process where [`LinkTransformation::new()`] needs to know what preprocessing
    /// to perform. Since the method takes `&self`, it can be called without
    /// conflicting with mutable borrows needed for [`LinkTransformation::apply()`].
    ///
    /// The returned [`LinkTransform`] is [`Copy`], so this method releases any
    /// borrows immediately, enabling the borrowing workaround pattern.
    pub(crate) fn transform_variant(&self) -> LinkTransform {
        match self.delegate {
            LinkTransformState::Keep => LinkTransform::Keep,
            LinkTransformState::Inline => LinkTransform::Inline,
            LinkTransformState::Reference(_) => LinkTransform::Reference,
            LinkTransformState::NeverInline(_) => LinkTransform::NeverInline,
        }
    }
}

/// Manages reference numbering for link transformations that need auto-assigned IDs.
///
/// This struct maintains the state needed to assign consistent, conflict-free
/// reference numbers to links. It's used by both [`LinkTransform::Reference`]
/// and [`LinkTransform::NeverInline`] strategies.
///
/// # Numbering Strategy
/// - Starts assigning numbers from 1
/// - Tracks existing numeric references to avoid conflicts
/// - Maintains a reordering map for consistent renumbering
/// - Advances the counter to skip over reserved numbers
///
/// # Usage Patterns
/// - **Reference transform**: Calls [`assign()`] to renumber all links
/// - **NeverInline transform**: Calls [`assign_new()`] for inline links and
///   [`reserve_if_numeric()`] for existing references
///
/// [`assign()`]: ReferenceAssigner::assign
/// [`assign_new()`]: ReferenceAssigner::assign_new
/// [`reserve_if_numeric()`]: ReferenceAssigner::reserve_if_numeric
struct ReferenceAssigner {
    /// The next number to assign to a new reference.
    ///
    /// Let's not worry about overflow. The minimum size for each link is 5 bytes (`[][1]`), so u64 of those is about
    ///  80 exabytes of markdown -- and it would be even bigger than that, since the digits get bigger. It's just not a
    /// case I'm too worried about right now.
    next_index: u64,

    /// Mappings from old numeric reference IDs to their new assigned numbers.
    ///
    /// This ensures that multiple references to the same ID get consistently
    /// renumbered. We store these as Strings (not ints) so that we don't need to worry
    /// about overflow when parsing very large numbers.
    reorderings: HashMap<String, u64>,
}

impl ReferenceAssigner {
    /// Creates a new assigner starting with reference number 1.
    ///
    /// The reordering map starts empty and will be populated as numeric
    /// references are encountered and need to be renumbered.
    fn new() -> Self {
        Self {
            next_index: 1,
            reorderings: HashMap::with_capacity(16), // arbitrary
        }
    }

    /// Assigns a reference ID for the given link using the Reference transform strategy.
    ///
    /// This method implements the full reference transformation logic:
    /// - **Inline links**: Get a new auto-assigned number
    /// - **Full references**: Renumber if numeric, otherwise keep as-is
    /// - **Collapsed/Shortcut**: Use the link text as reference ID, renumbering if it's numeric
    ///
    /// # Parameters
    /// - `state`: Preprocessing data from [`LinkTransformation::new()`], contains
    ///   extracted text for collapsed/shortcut links
    /// - `link`: The original link reference to transform
    ///
    /// # Reference ID Logic
    /// For collapsed/shortcut links, the link text becomes the reference ID:
    /// - `[example][]` → `[example][example]` (text is "example")
    /// - `[123][]` → `[123][1]` (text is "123", gets renumbered)
    /// - `[_123_][]` → `[_123_][_123_]` (text is "_123_", not purely numeric)
    fn assign<'md>(&mut self, state: LinkTransformation<'md>, link: &'md LinkReference) -> Cow<'md, LinkReference> {
        match &link {
            LinkReference::Inline => self.assign_new(),
            LinkReference::Full(prev) => self.assign_if_numeric(prev).unwrap_or(Cow::Borrowed(link)),
            LinkReference::Collapsed | LinkReference::Shortcut => {
                let text_cow = state.link_text.unwrap();
                self.assign_if_numeric(text_cow.deref()).unwrap_or_else(|| {
                    let ref_text_owned = String::from(text_cow.deref());
                    Cow::Owned(LinkReference::Full(ref_text_owned))
                })
            }
        }
    }

    /// Attempts to renumber a reference ID if it's purely numeric.
    ///
    /// This method handles the renumbering logic for numeric reference IDs:
    /// - If the ID is purely numeric (like "123"), it gets renumbered
    /// - If the ID contains non-digits (like "abc" or "_123_"), it's left unchanged
    /// - Uses the reordering map to ensure consistent renumbering of duplicate IDs
    ///
    /// # Parameters
    /// - `prev`: The existing reference ID to potentially renumber
    ///
    /// # Returns
    /// - `Some(new_reference)` if the ID was numeric and got renumbered
    /// - `None` if the ID was non-numeric and should be left unchanged
    ///
    /// # Reordering Logic
    /// - First encounter of "123" → maps to next available number, returns that number
    /// - Subsequent encounters of "123" → returns the same mapped number
    fn assign_if_numeric<'md>(&mut self, prev: &str) -> Option<Cow<'md, LinkReference>> {
        if prev.chars().all(|ch| ch.is_numeric()) {
            match self.reorderings.entry(String::from(prev)) {
                Entry::Occupied(map_to) => Some(Cow::Owned(LinkReference::Full(map_to.get().to_string()))),
                Entry::Vacant(e) => {
                    e.insert(self.next_index);
                    Some(self.assign_new())
                }
            }
        } else {
            None
        }
    }

    /// Assigns a new auto-incremented reference number.
    ///
    /// This method provides the core auto-numbering functionality:
    /// - Takes the current `next_index` value
    /// - Increments `next_index` for the next assignment
    /// - Returns a new [`LinkReference::Full`] with the assigned number
    ///
    /// Used for:
    /// - Converting inline links to references
    /// - Providing new numbers for renumbered numeric references
    fn assign_new<'md>(&mut self) -> Cow<'md, LinkReference> {
        let idx_str = self.next_index.to_string();
        self.next_index += 1;
        Cow::Owned(LinkReference::Full(idx_str))
    }

    /// Reserves a numeric reference without reassigning it.
    ///
    /// This method is used by [`LinkTransform::NeverInline`] to ensure that
    /// existing numeric references don't conflict with newly assigned numbers.
    /// Unlike [`assign_if_numeric()`], this doesn't change the reference ID,
    /// it just updates internal state to avoid future conflicts.
    ///
    /// # Parameters
    /// - `reference`: The reference ID to potentially reserve
    ///
    /// # Reservation Logic
    /// - If the reference is purely numeric (like "5"), parse it as a number
    /// - If that number is >= `next_index`, advance `next_index` to `number + 1`
    /// - This ensures future calls to [`assign_new()`] won't conflict
    ///
    /// # Example
    /// ```text
    /// next_index = 1
    /// reserve_if_numeric("5")  // next_index becomes 6
    /// assign_new()             // returns "6", not "1"
    /// ```
    ///
    /// [`assign_if_numeric()`]: ReferenceAssigner::assign_if_numeric
    /// [`assign_new()`]: ReferenceAssigner::assign_new
    fn reserve_if_numeric(&mut self, reference: &str) {
        if reference.chars().all(|ch| ch.is_numeric()) {
            if let Ok(num) = reference.parse::<u64>() {
                // Reserve this number by advancing next_index if needed
                if num >= self.next_index {
                    self.next_index = num + 1;
                }
            }
        }
    }
}

/// Converts a vector of inline elements back to markdown string format.
///
/// This function is used when we need to convert complex link labels (with formatting
/// like `**bold**` or `_italic_`) back to strings for use as reference IDs.
///
/// Unlike [`crate::output::fmt_plain_str::inlines_to_plain_string`], this preserves
/// formatting spans like emphasis, strong, etc., so `[**bold**][]` becomes `[**bold**][**bold**]`
/// rather than `[**bold**][bold]`.
///
/// # Parameters
/// - `inline_writer`: Configured writer for rendering inlines to markdown
/// - `inlines`: The inline elements to convert to string
///
/// # Usage
/// Called by [`LinkLabel::get_sort_string()`] when processing complex link labels
/// for the Reference transformation strategy.
fn inlines_to_string<'md>(inline_writer: &mut MdInlinesWriter<'md>, inlines: &'md Vec<Inline>) -> String {
    let mut string_writer = Output::without_text_wrapping(String::with_capacity(32)); // guess at capacity
    inline_writer.write_line(&mut string_writer, inlines);
    string_writer
        .take_underlying()
        .expect("internal error while parsing collapsed- or shortcut-style link")
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::util::utils_for_test::*;

    enum Combo {
        Of(LinkTransform, LinkReference),
    }

    variants_checker!(VARIANTS_CHECKER = Combo {
        Of(LinkTransform::Keep, LinkReference::Inline),
        Of(LinkTransform::Keep, LinkReference::Collapsed),
        Of(LinkTransform::Keep, LinkReference::Full(_)),
        Of(LinkTransform::Keep, LinkReference::Shortcut),

        Of(LinkTransform::Inline, LinkReference::Shortcut),
        Of(LinkTransform::Inline, LinkReference::Collapsed),
        Of(LinkTransform::Inline, LinkReference::Full(_)),
        Of(LinkTransform::Inline, LinkReference::Inline),

        Of(LinkTransform::Reference, LinkReference::Collapsed),
        Of(LinkTransform::Reference, LinkReference::Full(_)),
        Of(LinkTransform::Reference, LinkReference::Inline),
        Of(LinkTransform::Reference, LinkReference::Shortcut),

        Of(LinkTransform::NeverInline, LinkReference::Collapsed),
        Of(LinkTransform::NeverInline, LinkReference::Full(_)),
        Of(LinkTransform::NeverInline, LinkReference::Inline),
        Of(LinkTransform::NeverInline, LinkReference::Shortcut),
    });

    mod keep {
        use super::*;

        #[test]
        fn inline() {
            check_keep(LinkReference::Inline);
        }

        #[test]
        fn collapsed() {
            check_keep(LinkReference::Collapsed);
        }

        #[test]
        fn full() {
            check_keep(LinkReference::Full("5".to_string()));
        }

        #[test]
        fn shortcut() {
            check_keep(LinkReference::Shortcut);
        }

        fn check_keep(link_ref: LinkReference) {
            Given {
                transform: LinkTransform::Keep,
                label: mdq_inline!("doesn't matter"),
                orig_reference: link_ref.clone(),
            }
            .expect(link_ref);
        }
    }

    mod inline {
        use super::*;

        #[test]
        fn inline() {
            // We could in principle have this return a Borrowed Cow, since the input and output are both Inline.
            // But it's not really worth it, given that Inline is just a stateless enum variant and thus as cheap
            // (or potentially even cheaper!) than a pointer.
            check_inline(LinkReference::Inline);
        }

        #[test]
        fn collapsed() {
            check_inline(LinkReference::Collapsed);
        }

        #[test]
        fn full() {
            check_inline(LinkReference::Full("5".to_string()));
        }

        #[test]
        fn shortcut() {
            check_inline(LinkReference::Shortcut);
        }

        fn check_inline(link_ref: LinkReference) {
            Given {
                transform: LinkTransform::Inline,
                label: mdq_inline!("doesn't matter"),
                orig_reference: link_ref,
            }
            .expect(LinkReference::Inline);
        }
    }

    mod reference {
        use super::*;

        #[test]
        fn inline() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("doesn't matter"),
                orig_reference: LinkReference::Inline,
            }
            .expect(LinkReference::Full("1".to_string()));
        }

        #[test]
        fn collapsed_label_not_number() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("not a number"),
                orig_reference: LinkReference::Collapsed,
            }
            .expect(LinkReference::Full("not a number".to_string()));
        }

        #[test]
        fn collapsed_label_is_number() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("321"),
                orig_reference: LinkReference::Collapsed,
            }
            .expect(LinkReference::Full("1".to_string()));
        }

        #[test]
        fn full_ref_id_not_number() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("doesn't matter"),
                orig_reference: LinkReference::Full("non-number".to_string()),
            }
            .expect(LinkReference::Full("non-number".to_string()));
        }

        #[test]
        fn full_ref_id_is_number() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("doesn't matter"),
                orig_reference: LinkReference::Full("non-number".to_string()),
            }
            .expect(LinkReference::Full("non-number".to_string()));
        }

        #[test]
        fn full_ref_id_is_huge_number() {
            let huge_num_str = format!("{}00000", u128::MAX);
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("doesn't matter"),
                orig_reference: LinkReference::Full(huge_num_str),
            }
            .expect(LinkReference::Full("1".to_string()));
        }

        #[test]
        fn shortcut_label_not_number() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("not a number"),
                orig_reference: LinkReference::Shortcut,
            }
            .expect(LinkReference::Full("not a number".to_string()));
        }

        #[test]
        fn shortcut_label_is_number() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("321"),
                orig_reference: LinkReference::Shortcut,
            }
            .expect(LinkReference::Full("1".to_string()));
        }

        /// The label isn't even close to a number.
        ///
        /// _c.f._ [shortcut_label_inlines_are_emphasized_number]
        #[test]
        fn shortcut_label_inlines_not_number_like() {
            Given {
                transform: LinkTransform::Reference,
                label: mdq_inline!("hello world"),
                orig_reference: LinkReference::Shortcut,
            }
            .expect(LinkReference::Full("hello world".to_string()));
        }

        /// The label is kind of like a number, except that it's emphasized: `_123_`. This makes it not a number.
        ///
        /// _c.f._ [shortcut_label_inlines_not_number_like]
        #[test]
        fn shortcut_label_inlines_are_emphasized_number() {
            Given {
                transform: LinkTransform::Reference,
                label: Inline::Span(Span {
                    variant: SpanVariant::Emphasis,
                    children: vec![Inline::Text(Text {
                        variant: TextVariant::Plain,
                        value: "123".to_string(),
                    })],
                }),
                orig_reference: LinkReference::Shortcut,
            }
            .expect(LinkReference::Full("_123_".to_string()));
        }
    }

    mod never_inline {
        use super::*;

        #[test]
        fn inline() {
            // We could in principle have this return a Borrowed Cow, since the input and output are both Inline.
            // But it's not really worth it, given that Inline is just a stateless enum variant and thus as cheap
            // (or potentially even cheaper!) than a pointer.
            check_never_inline(LinkReference::Inline, LinkReference::Full("1".to_string()));
        }

        #[test]
        fn collapsed() {
            check_never_inline(LinkReference::Collapsed, LinkReference::Collapsed);
        }

        #[test]
        fn full() {
            check_never_inline(
                LinkReference::Full("5".to_string()),
                LinkReference::Full("5".to_string()),
            );
        }

        #[test]
        fn shortcut() {
            check_never_inline(LinkReference::Shortcut, LinkReference::Shortcut);
        }

        fn check_never_inline(link_ref: LinkReference, expect: LinkReference) {
            Given {
                transform: LinkTransform::NeverInline,
                label: mdq_inline!("doesn't matter"),
                orig_reference: link_ref,
            }
            .expect(expect);
        }
    }

    /// A smoke test basically to ensure that we increment values correctly. We won't test every transformation type,
    /// since the sibling sub-modules already do that.
    #[test]
    fn smoke_test_multi() {
        let mut transformer = LinkTransformer::from(LinkTransform::Reference);
        let ctx = MdContext::empty();
        let mut iw = MdInlinesWriter::new(
            &ctx,
            InlineElemOptions {
                link_format: LinkTransform::Keep,
                renumber_footnotes: false,
            },
        );

        // [alpha](https://example.com) ==> [alpha][1]
        let alpha = make_link("alpha", LinkReference::Inline);
        assert_eq!(
            transform(&mut transformer, &mut iw, &alpha),
            LinkReference::Full("1".to_string())
        );

        // [bravo][1] ==> [bravo][2]
        let bravo = make_link("bravo", LinkReference::Full("1".to_string()));
        assert_eq!(
            transform(&mut transformer, &mut iw, &bravo),
            LinkReference::Full("2".to_string())
        );

        // [charlie][] ==> [charlie][charlie]
        let charlie = make_link("charlie", LinkReference::Shortcut);
        assert_eq!(
            transform(&mut transformer, &mut iw, &charlie),
            LinkReference::Full("charlie".to_string())
        );

        // [delta][delta] ==> [delta][delta]
        let delta = make_link("delta", LinkReference::Full("delta".to_string()));
        assert_eq!(
            transform(&mut transformer, &mut iw, &delta),
            LinkReference::Full("delta".to_string())
        );

        // [789] ==> [789][3]
        let echo = make_link("789", LinkReference::Collapsed);
        assert_eq!(
            transform(&mut transformer, &mut iw, &echo),
            LinkReference::Full("3".to_string())
        );

        // [echo] ==> [echo][echo]
        let echo = make_link("echo", LinkReference::Collapsed);
        assert_eq!(
            transform(&mut transformer, &mut iw, &echo),
            LinkReference::Full("echo".to_string())
        );
    }

    #[test]
    fn smoke_test_multi_with_never_inline() {
        let mut transformer = LinkTransformer::from(LinkTransform::NeverInline);
        let ctx = MdContext::empty();
        let mut iw = MdInlinesWriter::new(
            &ctx,
            InlineElemOptions {
                link_format: LinkTransform::Keep,
                renumber_footnotes: false,
            },
        );

        // Start with a collapse, just for fun. It should be unchanged.
        let alpha = make_link("alpha", LinkReference::Collapsed);
        assert_eq!(transform(&mut transformer, &mut iw, &alpha), LinkReference::Collapsed);

        // Inline should turn into [bravo][1]
        let bravo = make_link("bravo", LinkReference::Inline);
        assert_eq!(
            transform(&mut transformer, &mut iw, &bravo),
            LinkReference::Full("1".to_string())
        );

        // [charlie][2] should be unchanged, *and* take up the "2" slot
        let charlie = make_link("charlie", LinkReference::Full("2".to_string()));
        assert_eq!(
            transform(&mut transformer, &mut iw, &charlie),
            LinkReference::Full("2".to_string())
        );

        // The next inline should turn into [bravo][3] -- not [2], because the previous full ref already took that.
        let delta = make_link("delta", LinkReference::Inline);
        assert_eq!(
            transform(&mut transformer, &mut iw, &delta),
            LinkReference::Full("3".to_string())
        );
    }

    fn transform<'md>(
        transformer: &mut LinkTransformer,
        iw: &mut MdInlinesWriter<'md>,
        link: &'md Link,
    ) -> LinkReference {
        let actual = match link {
            Link::Standard(standard_link) => {
                LinkTransformation::new(transformer.transform_variant(), iw, standard_link)
                    .apply(transformer, &standard_link.link.reference)
            }
            Link::Autolink(autolink) => {
                panic!("unexpected autolink: {autolink:?}")
            }
        };
        actual
    }

    fn make_link(label: &str, link_ref: LinkReference) -> Link {
        Link::Standard(StandardLink {
            display: vec![Inline::Text(Text {
                variant: TextVariant::Plain,
                value: label.to_string(),
            })],
            link: LinkDefinition {
                url: "https://example.com".to_string(),
                title: None,
                reference: link_ref,
            },
        })
    }

    struct Given {
        transform: LinkTransform,
        label: Inline,
        orig_reference: LinkReference,
    }

    impl Given {
        fn expect(self, expected: LinkReference) {
            let Given {
                transform,
                label,
                orig_reference: reference,
            } = self;
            let mut transformer = LinkTransformer::from(transform);
            let ctx = MdContext::empty();
            let mut iw = MdInlinesWriter::new(
                &ctx,
                InlineElemOptions {
                    link_format: LinkTransform::Keep,
                    renumber_footnotes: false,
                },
            );
            let link = Link::Standard(StandardLink {
                display: vec![label],
                link: LinkDefinition {
                    url: "https://example.com".to_string(),
                    title: None,
                    reference: reference.clone(),
                },
            });

            let actual = self::transform(&mut transformer, &mut iw, &link);

            VARIANTS_CHECKER.see(&Combo::Of(transform, reference.clone()));

            assert_eq!(actual, expected);
        }
    }
}
